"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/flashcards/FlashcardStudyMode.tsx":
/*!**********************************************************!*\
  !*** ./src/components/flashcards/FlashcardStudyMode.tsx ***!
  \**********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_IoArrowBack_IoArrowForward_react_icons_io5__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=IoArrowBack,IoArrowForward!=!react-icons/io5 */ \"(app-pages-browser)/./node_modules/react-icons/io5/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_FiCheck_FiHelpCircle_FiRotateCw_FiThumbsDown_FiThumbsUp_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=FiCheck,FiHelpCircle,FiRotateCw,FiThumbsDown,FiThumbsUp!=!react-icons/fi */ \"(app-pages-browser)/./node_modules/react-icons/fi/index.mjs\");\n// src/components/flashcards/FlashcardStudyMode.tsx\n\nvar _s = $RefreshSig$();\n\n\n\n\nconst FlashcardStudyMode = (param)=>{\n    let { flashcards, activeIndex, respondiendo, onRespuesta, onNavigate, onVolver, onReiniciarProgreso, onVerHistorial } = param;\n    var _currentFlashcard_progreso, _currentFlashcard_progreso1;\n    _s();\n    const currentFlashcard = flashcards[activeIndex];\n    const [isFlipped, setIsFlipped] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false); // Único estado para controlar el volteo\n    // Efecto para resetear el estado de volteo cuando cambia la tarjeta (activeIndex)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FlashcardStudyMode.useEffect\": ()=>{\n            console.log('FlashcardStudyMode: Resetting isFlipped to false for activeIndex:', activeIndex);\n            setIsFlipped(false); // Siempre empezar mostrando la pregunta\n        }\n    }[\"FlashcardStudyMode.useEffect\"], [\n        activeIndex\n    ]); // Solo depende de activeIndex\n    // Debug: Log del estado actual\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FlashcardStudyMode.useEffect\": ()=>{\n            console.log('FlashcardStudyMode: isFlipped state changed to:', isFlipped);\n        }\n    }[\"FlashcardStudyMode.useEffect\"], [\n        isFlipped\n    ]);\n    const handleCardFlip = (e)=>{\n        // Prevenir volteo si el clic fue en un botón dentro de la tarjeta\n        if (e) {\n            const target = e.target;\n            if (target.closest('button')) {\n                return;\n            }\n        }\n        if (respondiendo) return; // No voltear si se está procesando una respuesta\n        setIsFlipped((prev)=>!prev);\n    };\n    const handleDifficultyClick = (e, dificultad)=>{\n        e.stopPropagation(); // Prevenir que el clic en el botón voltee la tarjeta\n        if (respondiendo) return;\n        onRespuesta(dificultad);\n    };\n    const handleOptionalButtonClick = (e, action)=>{\n        e.stopPropagation();\n        if (respondiendo) return;\n        action();\n    };\n    if (!currentFlashcard) {\n        // Esto puede pasar brevemente si flashcards está vacío o activeIndex es inválido\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col items-center justify-center h-96 text-gray-500\",\n            children: [\n                \"Cargando tarjeta...\",\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: onVolver,\n                    className: \"mt-4 flex items-center text-blue-600 hover:text-blue-800\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IoArrowBack_IoArrowForward_react_icons_io5__WEBPACK_IMPORTED_MODULE_2__.IoArrowBack, {\n                            className: \"mr-1\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v4\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyMode.tsx\",\n                            lineNumber: 76,\n                            columnNumber: 11\n                        }, undefined),\n                        \" Volver\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v4\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyMode.tsx\",\n                    lineNumber: 72,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v4\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyMode.tsx\",\n            lineNumber: 70,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col items-center w-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full flex justify-between items-center mb-4 px-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: onVolver,\n                        className: \"flex items-center text-sm text-gray-600 hover:text-gray-900 p-2 rounded-md hover:bg-gray-100 transition-colors\",\n                        disabled: respondiendo,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IoArrowBack_IoArrowForward_react_icons_io5__WEBPACK_IMPORTED_MODULE_2__.IoArrowBack, {\n                                className: \"mr-1\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v4\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyMode.tsx\",\n                                lineNumber: 90,\n                                columnNumber: 11\n                            }, undefined),\n                            \" Volver\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v4\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyMode.tsx\",\n                        lineNumber: 85,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm text-gray-500\",\n                        children: [\n                            activeIndex + 1,\n                            \" / \",\n                            flashcards.length\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v4\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyMode.tsx\",\n                        lineNumber: 92,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v4\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyMode.tsx\",\n                lineNumber: 84,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full max-w-2xl mx-auto\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative w-full h-[24rem] sm:h-[28rem] perspective-1000\",\n                    onClick: ()=>handleCardFlip(),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                        className: \"absolute w-full h-full transform-style-3d\" // Crucial para que los hijos se transformen en 3D\n                        ,\n                        animate: {\n                            rotateY: isFlipped ? 180 : 0\n                        },\n                        transition: {\n                            duration: 0.6\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute w-full h-full backface-hidden bg-white rounded-xl shadow-xl border border-gray-200 p-6 flex flex-col justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs text-gray-400 mb-2\",\n                                                children: [\n                                                    ((_currentFlashcard_progreso = currentFlashcard.progreso) === null || _currentFlashcard_progreso === void 0 ? void 0 : _currentFlashcard_progreso.estado) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"px-2 py-0.5 rounded-full font-medium \".concat(currentFlashcard.progreso.estado === 'nuevo' ? 'bg-blue-100 text-blue-700' : currentFlashcard.progreso.estado === 'aprendiendo' ? 'bg-yellow-100 text-yellow-700' : currentFlashcard.progreso.estado === 'repasando' ? 'bg-orange-100 text-orange-700' : 'bg-green-100 text-green-700'),\n                                                        children: currentFlashcard.progreso.estado.charAt(0).toUpperCase() + currentFlashcard.progreso.estado.slice(1)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v4\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyMode.tsx\",\n                                                        lineNumber: 114,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    !((_currentFlashcard_progreso1 = currentFlashcard.progreso) === null || _currentFlashcard_progreso1 === void 0 ? void 0 : _currentFlashcard_progreso1.estado) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"bg-blue-100 text-blue-700 px-2 py-0.5 rounded-full font-medium text-xs\",\n                                                        children: \"Nuevo\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v4\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyMode.tsx\",\n                                                        lineNumber: 121,\n                                                        columnNumber: 60\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v4\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyMode.tsx\",\n                                                lineNumber: 112,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-grow flex items-center justify-center min-h-[150px] sm:min-h-[200px]\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg sm:text-xl font-semibold text-center text-gray-800 break-words\",\n                                                    children: currentFlashcard.pregunta\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v4\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyMode.tsx\",\n                                                    lineNumber: 124,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v4\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyMode.tsx\",\n                                                lineNumber: 123,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v4\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyMode.tsx\",\n                                        lineNumber: 110,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center mt-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: (e)=>{\n                                                e.stopPropagation();\n                                                handleCardFlip();\n                                            },\n                                            className: \"bg-blue-500 hover:bg-blue-600 text-white font-semibold py-2 px-6 rounded-lg text-sm transition-colors\",\n                                            disabled: respondiendo,\n                                            children: \"Mostrar respuesta\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v4\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyMode.tsx\",\n                                            lineNumber: 130,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v4\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyMode.tsx\",\n                                        lineNumber: 129,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v4\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyMode.tsx\",\n                                lineNumber: 109,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute w-full h-full backface-hidden bg-white rounded-xl shadow-xl border border-gray-200 p-6 flex flex-col justify-between rotate-y-180\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-grow flex items-center justify-center min-h-[150px] sm:min-h-[200px] overflow-y-auto\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-base sm:text-lg text-center text-gray-700 whitespace-pre-wrap break-words transform-none\",\n                                            children: currentFlashcard.respuesta\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v4\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyMode.tsx\",\n                                            lineNumber: 143,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v4\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyMode.tsx\",\n                                        lineNumber: 142,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-4 space-y-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-center text-sm font-medium text-gray-600\",\n                                                children: \"\\xbfQu\\xe9 tal te ha resultado?\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v4\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyMode.tsx\",\n                                                lineNumber: 148,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-around space-x-2 sm:space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: (e)=>handleDifficultyClick(e, 'dificil'),\n                                                        disabled: respondiendo,\n                                                        className: \"flex-1 flex flex-col items-center p-3 rounded-lg bg-red-50 hover:bg-red-100 text-red-600 transition-colors disabled:opacity-50\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiCheck_FiHelpCircle_FiRotateCw_FiThumbsDown_FiThumbsUp_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiThumbsDown, {\n                                                                className: \"mb-1 text-xl\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v4\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyMode.tsx\",\n                                                                lineNumber: 155,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            \" \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs font-medium\",\n                                                                children: \"Dif\\xedcil\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v4\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyMode.tsx\",\n                                                                lineNumber: 155,\n                                                                columnNumber: 63\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v4\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyMode.tsx\",\n                                                        lineNumber: 150,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: (e)=>handleDifficultyClick(e, 'normal'),\n                                                        disabled: respondiendo,\n                                                        className: \"flex-1 flex flex-col items-center p-3 rounded-lg bg-yellow-50 hover:bg-yellow-100 text-yellow-600 transition-colors disabled:opacity-50\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiCheck_FiHelpCircle_FiRotateCw_FiThumbsDown_FiThumbsUp_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiCheck, {\n                                                                className: \"mb-1 text-xl\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v4\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyMode.tsx\",\n                                                                lineNumber: 162,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            \" \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs font-medium\",\n                                                                children: \"Normal\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v4\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyMode.tsx\",\n                                                                lineNumber: 162,\n                                                                columnNumber: 58\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v4\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyMode.tsx\",\n                                                        lineNumber: 157,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: (e)=>handleDifficultyClick(e, 'facil'),\n                                                        disabled: respondiendo,\n                                                        className: \"flex-1 flex flex-col items-center p-3 rounded-lg bg-green-50 hover:bg-green-100 text-green-600 transition-colors disabled:opacity-50\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiCheck_FiHelpCircle_FiRotateCw_FiThumbsDown_FiThumbsUp_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiThumbsUp, {\n                                                                className: \"mb-1 text-xl\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v4\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyMode.tsx\",\n                                                                lineNumber: 169,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            \" \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs font-medium\",\n                                                                children: \"F\\xe1cil\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v4\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyMode.tsx\",\n                                                                lineNumber: 169,\n                                                                columnNumber: 61\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v4\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyMode.tsx\",\n                                                        lineNumber: 164,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v4\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyMode.tsx\",\n                                                lineNumber: 149,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            (onReiniciarProgreso || onVerHistorial) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-center space-x-4 pt-2 text-xs\",\n                                                children: [\n                                                    onReiniciarProgreso && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: (e)=>handleOptionalButtonClick(e, ()=>onReiniciarProgreso(currentFlashcard.id)),\n                                                        disabled: respondiendo,\n                                                        className: \"text-gray-500 hover:text-gray-700 underline flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiCheck_FiHelpCircle_FiRotateCw_FiThumbsDown_FiThumbsUp_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiRotateCw, {\n                                                                size: 12,\n                                                                className: \"mr-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v4\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyMode.tsx\",\n                                                                lineNumber: 180,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            \" Reiniciar\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v4\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyMode.tsx\",\n                                                        lineNumber: 175,\n                                                        columnNumber: 24\n                                                    }, undefined),\n                                                    onVerHistorial && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: (e)=>handleOptionalButtonClick(e, ()=>onVerHistorial(currentFlashcard.id)),\n                                                        disabled: respondiendo,\n                                                        className: \"text-blue-500 hover:text-blue-700 underline flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiCheck_FiHelpCircle_FiRotateCw_FiThumbsDown_FiThumbsUp_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiHelpCircle, {\n                                                                size: 12,\n                                                                className: \"mr-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v4\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyMode.tsx\",\n                                                                lineNumber: 189,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            \" Ver Historial\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v4\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyMode.tsx\",\n                                                        lineNumber: 184,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v4\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyMode.tsx\",\n                                                lineNumber: 173,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v4\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyMode.tsx\",\n                                        lineNumber: 147,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v4\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyMode.tsx\",\n                                lineNumber: 141,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v4\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyMode.tsx\",\n                        lineNumber: 103,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v4\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyMode.tsx\",\n                    lineNumber: 99,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v4\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyMode.tsx\",\n                lineNumber: 97,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full max-w-2xl mx-auto flex justify-between mt-6 px-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>onNavigate('prev'),\n                        disabled: activeIndex === 0 || respondiendo,\n                        className: \"flex items-center text-sm p-2 rounded-md transition-colors \".concat(activeIndex === 0 || respondiendo ? 'text-gray-400 cursor-not-allowed' : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IoArrowBack_IoArrowForward_react_icons_io5__WEBPACK_IMPORTED_MODULE_2__.IoArrowBack, {\n                                className: \"mr-1\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v4\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyMode.tsx\",\n                                lineNumber: 207,\n                                columnNumber: 11\n                            }, undefined),\n                            \" Anterior\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v4\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyMode.tsx\",\n                        lineNumber: 202,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>onNavigate('next'),\n                        disabled: activeIndex === flashcards.length - 1 || respondiendo,\n                        className: \"flex items-center text-sm p-2 rounded-md transition-colors \".concat(activeIndex === flashcards.length - 1 || respondiendo ? 'text-gray-400 cursor-not-allowed' : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'),\n                        children: [\n                            \"Siguiente \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IoArrowBack_IoArrowForward_react_icons_io5__WEBPACK_IMPORTED_MODULE_2__.IoArrowForward, {\n                                className: \"ml-1\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v4\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyMode.tsx\",\n                                lineNumber: 214,\n                                columnNumber: 21\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v4\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyMode.tsx\",\n                        lineNumber: 209,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v4\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyMode.tsx\",\n                lineNumber: 201,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v4\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyMode.tsx\",\n        lineNumber: 83,\n        columnNumber: 5\n    }, undefined);\n};\n_s(FlashcardStudyMode, \"pCQCXReq+Qk/DY4YC9KTjj0FnW4=\");\n_c = FlashcardStudyMode;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (FlashcardStudyMode);\nvar _c;\n$RefreshReg$(_c, \"FlashcardStudyMode\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/flashcards/FlashcardStudyMode.tsx\n"));

/***/ })

});