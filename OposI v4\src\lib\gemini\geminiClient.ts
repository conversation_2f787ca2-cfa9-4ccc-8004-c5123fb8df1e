import { GoogleGenerativeAI, HarmCategory, HarmBlockThreshold } from '@google/generative-ai';

// Configuración de la API de Gemini
const API_KEY = process.env.NEXT_PUBLIC_GEMINI_API_KEY || '';
const MODEL_NAME = 'gemini-2.5-flash-preview-05-20';

// Verificar que la API key esté configurada
if (!API_KEY) {
  console.error('NEXT_PUBLIC_GEMINI_API_KEY no está configurada en las variables de entorno');
}

// Inicializar el cliente de Gemini
export const genAI = new GoogleGenerativeAI(API_KEY);
export const model = genAI.getGenerativeModel({
  model: MODEL_NAME,
  safetySettings: [
    {
      category: HarmCategory.HARM_CATEGORY_HARASSMENT,
      threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
    },
    {
      category: HarmCategory.HARM_CATEGORY_HATE_SPEECH,
      threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
    },
    {
      category: HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT,
      threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
    },
    {
      category: HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT,
      threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
    },
  ],
});

// Ya no usamos instrucciones base globales, cada servicio usa su propio prompt personalizado

/**
 * Trunca el contenido de un documento si es demasiado largo
 */
export function truncarContenido(contenido: string | undefined | null, maxLength: number = 25000): string {
  // Verificar que el contenido sea una cadena válida
  if (contenido === undefined || contenido === null) {
    console.warn('Se intentó truncar un contenido undefined o null');
    return '';
  }

  // Asegurarse de que el contenido sea una cadena
  const contenidoStr = String(contenido);

  if (contenidoStr.length <= maxLength) {
    return contenidoStr;
  }

  return contenidoStr.substring(0, maxLength) +
    `\n\n[CONTENIDO TRUNCADO: El documento original es más largo. Esta es una versión reducida para procesamiento.]`;
}

/**
 * Prepara los documentos para enviarlos al modelo
 */
export function prepararDocumentos(documentos: { titulo: string; contenido: string; categoria?: string; numero_tema?: number }[]): string {
  // Verificar que documentos sea un array válido
  if (!documentos || !Array.isArray(documentos) || documentos.length === 0) {
    console.warn('No se proporcionaron documentos válidos para prepararDocumentos');
    return '';
  }

  try {
    return documentos.map(doc => {
      // Verificar que doc sea un objeto válido con las propiedades necesarias
      if (!doc || typeof doc !== 'object') {
        console.warn('Documento inválido en prepararDocumentos:', doc);
        return '';
      }

      // Verificar que doc.titulo y doc.contenido existan
      if (!doc.titulo || !doc.contenido) {
        console.warn('Documento sin título o contenido en prepararDocumentos:', doc);
        return '';
      }

      const categoria = doc.categoria ? `[${doc.categoria}]` : '';
      const numeroTema = doc.numero_tema ? `Tema ${doc.numero_tema}: ` : '';
      const titulo = `${categoria} ${numeroTema}${doc.titulo}`;

      return `
=== DOCUMENTO: ${titulo.trim()} ===
${truncarContenido(doc.contenido)}
=== FIN DEL DOCUMENTO ===
`;
    }).filter(Boolean).join('\n\n'); // Filtrar elementos vacíos
  } catch (error) {
    console.error('Error al preparar documentos:', error);
    return '';
  }
}
