"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/flashcards/FlashcardStudyMode.tsx":
/*!**********************************************************!*\
  !*** ./src/components/flashcards/FlashcardStudyMode.tsx ***!
  \**********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_IoArrowBack_IoArrowForward_react_icons_io5__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=IoArrowBack,IoArrowForward!=!react-icons/io5 */ \"(app-pages-browser)/./node_modules/react-icons/io5/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_FiCheck_FiHelpCircle_FiRotateCw_FiThumbsDown_FiThumbsUp_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=FiCheck,FiHelpCircle,FiRotateCw,FiThumbsDown,FiThumbsUp!=!react-icons/fi */ \"(app-pages-browser)/./node_modules/react-icons/fi/index.mjs\");\n// src/components/flashcards/FlashcardStudyMode.tsx\n\nvar _s = $RefreshSig$();\n\n\n\n\nconst FlashcardStudyMode = (param)=>{\n    let { flashcards, activeIndex, respondiendo, onRespuesta, onNavigate, onVolver, onReiniciarProgreso, onVerHistorial } = param;\n    var _currentFlashcard_progreso, _currentFlashcard_progreso1;\n    _s();\n    const currentFlashcard = flashcards[activeIndex];\n    const [isFlipped, setIsFlipped] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false); // Único estado para controlar el volteo\n    // Efecto para resetear el estado de volteo cuando cambia la tarjeta (activeIndex)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FlashcardStudyMode.useEffect\": ()=>{\n            console.log('FlashcardStudyMode: Resetting isFlipped to false for activeIndex:', activeIndex);\n            setIsFlipped(false); // Siempre empezar mostrando la pregunta\n        }\n    }[\"FlashcardStudyMode.useEffect\"], [\n        activeIndex\n    ]); // Solo depende de activeIndex\n    // Debug: Log del estado actual\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FlashcardStudyMode.useEffect\": ()=>{\n            console.log('FlashcardStudyMode: isFlipped state changed to:', isFlipped);\n        }\n    }[\"FlashcardStudyMode.useEffect\"], [\n        isFlipped\n    ]);\n    // Debug: Log de la flashcard actual\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FlashcardStudyMode.useEffect\": ()=>{\n            if (currentFlashcard) {\n                console.log('FlashcardStudyMode: Current flashcard:', {\n                    pregunta: currentFlashcard.pregunta,\n                    respuesta: currentFlashcard.respuesta,\n                    activeIndex\n                });\n            }\n        }\n    }[\"FlashcardStudyMode.useEffect\"], [\n        currentFlashcard,\n        activeIndex\n    ]);\n    const handleCardFlip = (e)=>{\n        // Prevenir volteo si el clic fue en un botón dentro de la tarjeta\n        if (e) {\n            const target = e.target;\n            if (target.closest('button')) {\n                return;\n            }\n        }\n        if (respondiendo) return; // No voltear si se está procesando una respuesta\n        setIsFlipped((prev)=>!prev);\n    };\n    const handleDifficultyClick = (e, dificultad)=>{\n        e.stopPropagation(); // Prevenir que el clic en el botón voltee la tarjeta\n        if (respondiendo) return;\n        onRespuesta(dificultad);\n    };\n    const handleOptionalButtonClick = (e, action)=>{\n        e.stopPropagation();\n        if (respondiendo) return;\n        action();\n    };\n    if (!currentFlashcard) {\n        // Esto puede pasar brevemente si flashcards está vacío o activeIndex es inválido\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col items-center justify-center h-96 text-gray-500\",\n            children: [\n                \"Cargando tarjeta...\",\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: onVolver,\n                    className: \"mt-4 flex items-center text-blue-600 hover:text-blue-800\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IoArrowBack_IoArrowForward_react_icons_io5__WEBPACK_IMPORTED_MODULE_2__.IoArrowBack, {\n                            className: \"mr-1\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v4\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyMode.tsx\",\n                            lineNumber: 87,\n                            columnNumber: 11\n                        }, undefined),\n                        \" Volver\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v4\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyMode.tsx\",\n                    lineNumber: 83,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v4\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyMode.tsx\",\n            lineNumber: 81,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col items-center w-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full flex justify-between items-center mb-4 px-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: onVolver,\n                        className: \"flex items-center text-sm text-gray-600 hover:text-gray-900 p-2 rounded-md hover:bg-gray-100 transition-colors\",\n                        disabled: respondiendo,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IoArrowBack_IoArrowForward_react_icons_io5__WEBPACK_IMPORTED_MODULE_2__.IoArrowBack, {\n                                className: \"mr-1\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v4\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyMode.tsx\",\n                                lineNumber: 101,\n                                columnNumber: 11\n                            }, undefined),\n                            \" Volver\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v4\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyMode.tsx\",\n                        lineNumber: 96,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm text-gray-500\",\n                        children: [\n                            activeIndex + 1,\n                            \" / \",\n                            flashcards.length\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v4\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyMode.tsx\",\n                        lineNumber: 103,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v4\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyMode.tsx\",\n                lineNumber: 95,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full max-w-2xl mx-auto\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative w-full h-[24rem] sm:h-[28rem] perspective-1000\",\n                    onClick: ()=>handleCardFlip(),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                        className: \"absolute w-full h-full transform-style-3d\" // Crucial para que los hijos se transformen en 3D\n                        ,\n                        animate: {\n                            rotateY: isFlipped ? 180 : 0\n                        },\n                        transition: {\n                            duration: 0.6\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute w-full h-full backface-hidden bg-white rounded-xl shadow-xl border border-gray-200 p-6 flex flex-col justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs text-gray-400 mb-2\",\n                                                children: [\n                                                    ((_currentFlashcard_progreso = currentFlashcard.progreso) === null || _currentFlashcard_progreso === void 0 ? void 0 : _currentFlashcard_progreso.estado) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"px-2 py-0.5 rounded-full font-medium \".concat(currentFlashcard.progreso.estado === 'nuevo' ? 'bg-blue-100 text-blue-700' : currentFlashcard.progreso.estado === 'aprendiendo' ? 'bg-yellow-100 text-yellow-700' : currentFlashcard.progreso.estado === 'repasando' ? 'bg-orange-100 text-orange-700' : 'bg-green-100 text-green-700'),\n                                                        children: currentFlashcard.progreso.estado.charAt(0).toUpperCase() + currentFlashcard.progreso.estado.slice(1)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v4\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyMode.tsx\",\n                                                        lineNumber: 125,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    !((_currentFlashcard_progreso1 = currentFlashcard.progreso) === null || _currentFlashcard_progreso1 === void 0 ? void 0 : _currentFlashcard_progreso1.estado) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"bg-blue-100 text-blue-700 px-2 py-0.5 rounded-full font-medium text-xs\",\n                                                        children: \"Nuevo\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v4\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyMode.tsx\",\n                                                        lineNumber: 132,\n                                                        columnNumber: 60\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v4\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyMode.tsx\",\n                                                lineNumber: 123,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-grow flex items-center justify-center min-h-[150px] sm:min-h-[200px]\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg sm:text-xl font-semibold text-center text-gray-800 break-words\",\n                                                    children: currentFlashcard.pregunta\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v4\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyMode.tsx\",\n                                                    lineNumber: 135,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v4\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyMode.tsx\",\n                                                lineNumber: 134,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v4\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyMode.tsx\",\n                                        lineNumber: 121,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center mt-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: (e)=>{\n                                                e.stopPropagation();\n                                                handleCardFlip();\n                                            },\n                                            className: \"bg-blue-500 hover:bg-blue-600 text-white font-semibold py-2 px-6 rounded-lg text-sm transition-colors\",\n                                            disabled: respondiendo,\n                                            children: \"Mostrar respuesta\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v4\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyMode.tsx\",\n                                            lineNumber: 141,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v4\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyMode.tsx\",\n                                        lineNumber: 140,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v4\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyMode.tsx\",\n                                lineNumber: 120,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute w-full h-full backface-hidden bg-white rounded-xl shadow-xl border border-gray-200 p-6 flex flex-col justify-between rotate-y-180\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-grow flex items-center justify-center min-h-[150px] sm:min-h-[200px] overflow-y-auto\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-base sm:text-lg text-center text-gray-700 whitespace-pre-wrap break-words transform-none\",\n                                            children: currentFlashcard.respuesta\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v4\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyMode.tsx\",\n                                            lineNumber: 154,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v4\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyMode.tsx\",\n                                        lineNumber: 153,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-4 space-y-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-center text-sm font-medium text-gray-600\",\n                                                children: \"\\xbfQu\\xe9 tal te ha resultado?\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v4\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyMode.tsx\",\n                                                lineNumber: 159,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-around space-x-2 sm:space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: (e)=>handleDifficultyClick(e, 'dificil'),\n                                                        disabled: respondiendo,\n                                                        className: \"flex-1 flex flex-col items-center p-3 rounded-lg bg-red-50 hover:bg-red-100 text-red-600 transition-colors disabled:opacity-50\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiCheck_FiHelpCircle_FiRotateCw_FiThumbsDown_FiThumbsUp_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiThumbsDown, {\n                                                                className: \"mb-1 text-xl\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v4\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyMode.tsx\",\n                                                                lineNumber: 166,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            \" \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs font-medium\",\n                                                                children: \"Dif\\xedcil\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v4\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyMode.tsx\",\n                                                                lineNumber: 166,\n                                                                columnNumber: 63\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v4\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyMode.tsx\",\n                                                        lineNumber: 161,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: (e)=>handleDifficultyClick(e, 'normal'),\n                                                        disabled: respondiendo,\n                                                        className: \"flex-1 flex flex-col items-center p-3 rounded-lg bg-yellow-50 hover:bg-yellow-100 text-yellow-600 transition-colors disabled:opacity-50\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiCheck_FiHelpCircle_FiRotateCw_FiThumbsDown_FiThumbsUp_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiCheck, {\n                                                                className: \"mb-1 text-xl\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v4\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyMode.tsx\",\n                                                                lineNumber: 173,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            \" \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs font-medium\",\n                                                                children: \"Normal\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v4\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyMode.tsx\",\n                                                                lineNumber: 173,\n                                                                columnNumber: 58\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v4\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyMode.tsx\",\n                                                        lineNumber: 168,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: (e)=>handleDifficultyClick(e, 'facil'),\n                                                        disabled: respondiendo,\n                                                        className: \"flex-1 flex flex-col items-center p-3 rounded-lg bg-green-50 hover:bg-green-100 text-green-600 transition-colors disabled:opacity-50\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiCheck_FiHelpCircle_FiRotateCw_FiThumbsDown_FiThumbsUp_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiThumbsUp, {\n                                                                className: \"mb-1 text-xl\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v4\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyMode.tsx\",\n                                                                lineNumber: 180,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            \" \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs font-medium\",\n                                                                children: \"F\\xe1cil\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v4\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyMode.tsx\",\n                                                                lineNumber: 180,\n                                                                columnNumber: 61\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v4\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyMode.tsx\",\n                                                        lineNumber: 175,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v4\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyMode.tsx\",\n                                                lineNumber: 160,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            (onReiniciarProgreso || onVerHistorial) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-center space-x-4 pt-2 text-xs\",\n                                                children: [\n                                                    onReiniciarProgreso && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: (e)=>handleOptionalButtonClick(e, ()=>onReiniciarProgreso(currentFlashcard.id)),\n                                                        disabled: respondiendo,\n                                                        className: \"text-gray-500 hover:text-gray-700 underline flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiCheck_FiHelpCircle_FiRotateCw_FiThumbsDown_FiThumbsUp_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiRotateCw, {\n                                                                size: 12,\n                                                                className: \"mr-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v4\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyMode.tsx\",\n                                                                lineNumber: 191,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            \" Reiniciar\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v4\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyMode.tsx\",\n                                                        lineNumber: 186,\n                                                        columnNumber: 24\n                                                    }, undefined),\n                                                    onVerHistorial && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: (e)=>handleOptionalButtonClick(e, ()=>onVerHistorial(currentFlashcard.id)),\n                                                        disabled: respondiendo,\n                                                        className: \"text-blue-500 hover:text-blue-700 underline flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiCheck_FiHelpCircle_FiRotateCw_FiThumbsDown_FiThumbsUp_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiHelpCircle, {\n                                                                size: 12,\n                                                                className: \"mr-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v4\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyMode.tsx\",\n                                                                lineNumber: 200,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            \" Ver Historial\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v4\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyMode.tsx\",\n                                                        lineNumber: 195,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v4\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyMode.tsx\",\n                                                lineNumber: 184,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v4\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyMode.tsx\",\n                                        lineNumber: 158,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v4\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyMode.tsx\",\n                                lineNumber: 152,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v4\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyMode.tsx\",\n                        lineNumber: 114,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v4\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyMode.tsx\",\n                    lineNumber: 110,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v4\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyMode.tsx\",\n                lineNumber: 108,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full max-w-2xl mx-auto flex justify-between mt-6 px-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>onNavigate('prev'),\n                        disabled: activeIndex === 0 || respondiendo,\n                        className: \"flex items-center text-sm p-2 rounded-md transition-colors \".concat(activeIndex === 0 || respondiendo ? 'text-gray-400 cursor-not-allowed' : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IoArrowBack_IoArrowForward_react_icons_io5__WEBPACK_IMPORTED_MODULE_2__.IoArrowBack, {\n                                className: \"mr-1\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v4\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyMode.tsx\",\n                                lineNumber: 218,\n                                columnNumber: 11\n                            }, undefined),\n                            \" Anterior\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v4\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyMode.tsx\",\n                        lineNumber: 213,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>onNavigate('next'),\n                        disabled: activeIndex === flashcards.length - 1 || respondiendo,\n                        className: \"flex items-center text-sm p-2 rounded-md transition-colors \".concat(activeIndex === flashcards.length - 1 || respondiendo ? 'text-gray-400 cursor-not-allowed' : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'),\n                        children: [\n                            \"Siguiente \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IoArrowBack_IoArrowForward_react_icons_io5__WEBPACK_IMPORTED_MODULE_2__.IoArrowForward, {\n                                className: \"ml-1\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v4\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyMode.tsx\",\n                                lineNumber: 225,\n                                columnNumber: 21\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v4\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyMode.tsx\",\n                        lineNumber: 220,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v4\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyMode.tsx\",\n                lineNumber: 212,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v4\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyMode.tsx\",\n        lineNumber: 94,\n        columnNumber: 5\n    }, undefined);\n};\n_s(FlashcardStudyMode, \"rrDUjvJn9RaDkbMmYoipto1jDpc=\");\n_c = FlashcardStudyMode;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (FlashcardStudyMode);\nvar _c;\n$RefreshReg$(_c, \"FlashcardStudyMode\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/flashcards/FlashcardStudyMode.tsx\n"));

/***/ })

});